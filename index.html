<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文管理应用</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 登录页面 -->
    <div id="login-page" class="page active">
        <div class="container">
            <div class="login-container">
                <div class="login-header">
                    <h1>作文管理系统</h1>
                    <p>欢迎使用智能作文管理平台</p>
                </div>
                
                <form class="login-form" id="loginForm">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember">
                            <span class="checkmark"></span>
                            记住我
                        </label>
                        <a href="#" class="forgot-password">忘记密码？</a>
                    </div>
                    
                    <button type="submit" class="login-btn">登录</button>
                </form>
                
                <div class="login-footer">
                    <p>还没有账户？ <a href="#" id="registerLink">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 首页 -->
    <div id="homepage" class="page">
        <div class="mobile-container">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-content">
                    <div class="user-greeting">
                        <h2>你好，同学</h2>
                        <p>今天也要努力写作哦！</p>
                    </div>
                    <div class="header-actions">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <button class="profile-btn" onclick="navigateTo('profile')">
                            <img src="assets/images/avatar.png" alt="头像" class="avatar">
                        </button>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <main class="main-content">
                <!-- 快速操作卡片 -->
                <section class="quick-actions">
                    <h3>快速操作</h3>
                    <div class="action-cards">
                        <div class="action-card" onclick="navigateTo('essay-requirements')">
                            <div class="card-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h4>新建作文</h4>
                            <p>开始创作新的作文</p>
                        </div>
                        
                        <div class="action-card" onclick="navigateTo('essay-management')">
                            <div class="card-icon">
                                <i class="fas fa-folder"></i>
                            </div>
                            <h4>作文管理</h4>
                            <p>查看和管理你的作文</p>
                        </div>
                        
                        <div class="action-card" onclick="navigateTo('history')">
                            <div class="card-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h4>历史记录</h4>
                            <p>查看写作历史</p>
                        </div>
                    </div>
                </section>

                <!-- 最近作文 -->
                <section class="recent-essays">
                    <div class="section-header">
                        <h3>最近作文</h3>
                        <a href="#" onclick="navigateTo('essay-management')">查看全部</a>
                    </div>
                    <div class="essay-list">
                        <div class="essay-item">
                            <div class="essay-info">
                                <h4>我的暑假生活</h4>
                                <p>记叙文 · 800字</p>
                                <span class="essay-date">2024-07-20</span>
                            </div>
                            <div class="essay-status">
                                <span class="status-badge completed">已完成</span>
                            </div>
                        </div>
                        
                        <div class="essay-item">
                            <div class="essay-info">
                                <h4>环保从我做起</h4>
                                <p>议论文 · 600字</p>
                                <span class="essay-date">2024-07-18</span>
                            </div>
                            <div class="essay-status">
                                <span class="status-badge draft">草稿</span>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

            <!-- 底部导航 -->
            <nav class="bottom-nav">
                <button class="nav-item active" onclick="navigateTo('homepage')">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </button>
                <button class="nav-item" onclick="navigateTo('essay-management')">
                    <i class="fas fa-file-alt"></i>
                    <span>作文</span>
                </button>
                <button class="nav-item" onclick="navigateTo('history')">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </button>
                <button class="nav-item" onclick="navigateTo('profile')">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </button>
            </nav>
        </div>
    </div>

    <!-- 作文要求页面 -->
    <div id="essay-requirements" class="page">
        <div class="mobile-container">
            <header class="page-header">
                <button class="back-btn" onclick="navigateTo('homepage')">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h2>作文要求</h2>
                <button class="help-btn">
                    <i class="fas fa-question-circle"></i>
                </button>
            </header>

            <main class="main-content">
                <form class="essay-form" id="essayForm">
                    <div class="form-section">
                        <h3>基本信息</h3>
                        
                        <div class="form-group">
                            <label for="essayTitle">作文标题</label>
                            <input type="text" id="essayTitle" name="essayTitle" placeholder="请输入作文标题" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="essayType">作文类型</label>
                            <select id="essayType" name="essayType" required>
                                <option value="">请选择作文类型</option>
                                <option value="narrative">记叙文</option>
                                <option value="argumentative">议论文</option>
                                <option value="expository">说明文</option>
                                <option value="descriptive">描写文</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="wordCount">字数要求</label>
                            <select id="wordCount" name="wordCount" required>
                                <option value="">请选择字数</option>
                                <option value="400">400字</option>
                                <option value="600">600字</option>
                                <option value="800">800字</option>
                                <option value="1000">1000字</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>写作要求</h3>
                        
                        <div class="form-group">
                            <label for="essayTopic">作文主题</label>
                            <textarea id="essayTopic" name="essayTopic" placeholder="请描述作文主题和要求..." rows="4" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label>特殊要求</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" name="requirements" value="structure">
                                    <span class="checkmark"></span>
                                    结构清晰
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="requirements" value="language">
                                    <span class="checkmark"></span>
                                    语言优美
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="requirements" value="creativity">
                                    <span class="checkmark"></span>
                                    创意新颖
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="navigateTo('homepage')">取消</button>
                        <button type="submit" class="btn-primary">开始写作</button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/forms.js"></script>
</body>
</html>
