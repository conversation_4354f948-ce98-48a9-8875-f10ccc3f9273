/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* 页面容器 */
.page {
    display: none;
    min-height: 100vh;
    position: relative;
}

.page.active {
    display: block;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.mobile-container {
    max-width: 414px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 登录页面样式 */
#login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    font-size: 14px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
}

.checkbox-container input {
    margin-right: 8px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-footer {
    text-align: center;
    color: #666;
    font-size: 14px;
}

.login-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

/* 顶部导航 */
.top-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 20px 30px;
    border-radius: 0 0 25px 25px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-greeting h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
}

.user-greeting p {
    font-size: 14px;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-btn, .profile-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-btn:hover, .profile-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.notification-btn i {
    color: white;
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

/* 主要内容 */
.main-content {
    padding: 25px 20px 100px;
}

/* 快速操作 */
.quick-actions h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.action-card {
    background: white;
    padding: 20px 15px;
    border-radius: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
}

.card-icon i {
    color: white;
    font-size: 20px;
}

.action-card h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.action-card p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 最近作文 */
.recent-essays {
    margin-top: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.section-header a {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.essay-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.essay-item {
    background: white;
    padding: 16px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.essay-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.essay-info h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.essay-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
}

.essay-date {
    font-size: 12px;
    color: #999;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.draft {
    background: #fff3cd;
    color: #856404;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: white;
    display: flex;
    justify-content: space-around;
    padding: 12px 0 20px;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.nav-item {
    background: none;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 12px;
    color: #999;
}

.nav-item.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 12px;
    font-weight: 500;
}

.nav-item:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* 页面头部 */
.page-header {
    background: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn, .help-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.back-btn:hover, .help-btn:hover {
    background: #f8f9fa;
    color: #667eea;
}

.page-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 表单样式 */
.essay-form {
    padding: 20px;
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
    margin-right: 12px;
    transform: scale(1.2);
}

.checkbox-item span {
    font-size: 14px;
    color: #333;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.btn-primary,
.btn-secondary {
    flex: 1;
    padding: 14px 20px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #ced4da;
}

/* 自定义复选框样式 */
.checkbox-container,
.checkbox-item {
    position: relative;
}

.checkbox-container .checkmark,
.checkbox-item .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #f8f9fa;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.checkbox-container input,
.checkbox-item input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-container input:checked ~ .checkmark,
.checkbox-item input:checked ~ .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after,
.checkbox-item input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after,
.checkbox-item .checkmark:after {
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}
